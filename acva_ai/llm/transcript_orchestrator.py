import logging
from enum import Enum
from typing import Dict, List, Optional, Union
from pydub import AudioSegment
from acva_ai._params import FALLBACK_TRANSCRIPT_PROVIDER
from acva_ai.llm.providers.transcript_provider import TranscriptProvider

from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


class AudioTranscriptionError(Exception):
    """Custom exception for audio transcription errors."""

    pass


class AudioFileError(Exception):
    """Custom exception for audio file related errors."""

    pass


class TranscriptOrchestrator:
    """
    Orchestrates calls to different transcript providers with fallback capabilities.

    Similar to LLMOrchestrator but specifically for audio transcription services.
    """

    def __init__(
        self,
        primary_provider: TranscriptProvider,
        max_retries: int = 3,
        retry_delay: float = 5.0,
    ):
        """
        Initialize the transcript orchestrator.

        Args:
            primary_provider: The primary transcript provider to use
            max_retries: Maximum number of retries per provider
            retry_delay: Initial delay before retrying (will increase exponentially)
        """
        self.primary_provider = primary_provider
        self.fallback_providers = self._get_default_fallbacks(primary_provider)
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self._providers: Dict[TranscriptProvider, object] = {}

        # Initialize providers
        self._initialize_providers()

    def _get_default_fallbacks(
        self, primary: TranscriptProvider
    ) -> List[TranscriptProvider]:
        """
        Get default fallback providers based on the primary provider.
        Uses the fallback providers from params, excluding the primary provider.

        Args:
            primary: The primary provider

        Returns:
            List of fallback providers
        """
        fallback_candidates = [provider for provider in FALLBACK_TRANSCRIPT_PROVIDER]

        # Convert to TranscriptProvider enums and exclude the primary provider
        fallback_providers = []
        for provider_name in fallback_candidates:
            try:
                provider = TranscriptProvider(provider_name)
                if provider != primary:
                    fallback_providers.append(provider)
            except ValueError:
                # Skip unsupported providers
                logger.warning(
                    f"Unsupported transcript provider in fallback list: {provider_name}"
                )
                continue

        return fallback_providers

    def _initialize_providers(self):
        """Initialize provider instances."""
        # Import providers here to avoid circular imports
        from acva_ai.llm.providers.elevenlabs_transcript_provider import (
            ElevenLabsTranscriptProvider,
        )
        from acva_ai.llm.providers.azure_transcript_provider import (
            AzureTranscriptProvider,
        )
        from acva_ai.llm.providers.openai_transcript_provider import (
            OpenAITranscriptProvider,
        )

        provider_classes = {
            TranscriptProvider.ELEVENLABS: ElevenLabsTranscriptProvider,
            TranscriptProvider.AZURE: AzureTranscriptProvider,
            TranscriptProvider.OPENAI: OpenAITranscriptProvider,
        }

        # Initialize all providers that might be used
        all_providers = [self.primary_provider] + self.fallback_providers
        for provider in set(all_providers):
            if provider in provider_classes:
                self._providers[provider] = provider_classes[provider]()
            else:
                logger.warning(f"Unknown transcript provider: {provider}")

    async def transcribe_audio_file(
        self,
        audio_file_path: str,
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        **kwargs,
    ) -> str:
        """
        Transcribe audio from a file path using the orchestrator.

        Args:
            audio_file_path: Path to the audio file
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            **kwargs: Additional provider-specific parameters

        Returns:
            Transcribed text as string

        Raises:
            AudioFileError: If there are issues with the audio file
            AudioTranscriptionError: If all providers fail
        """
        return await self._call_with_fallback(
            "transcribe_audio_file",
            audio_file_path=audio_file_path,
            language=language,
            use_cache=use_cache,
            response_usage=response_usage,
            max_file_size_mb=max_file_size_mb,
            **kwargs,
        )

    async def transcribe_audio_segment(
        self,
        audio_segment: AudioSegment,
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        **kwargs,
    ) -> str:
        """
        Transcribe audio from an AudioSegment using the orchestrator.

        Args:
            audio_segment: AudioSegment object to transcribe
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            **kwargs: Additional provider-specific parameters

        Returns:
            Transcribed text as string

        Raises:
            AudioFileError: If there are issues with the AudioSegment
            AudioTranscriptionError: If all providers fail
        """
        return await self._call_with_fallback(
            "transcribe_audio_segment",
            audio_segment=audio_segment,
            language=language,
            use_cache=use_cache,
            response_usage=response_usage,
            max_file_size_mb=max_file_size_mb,
            **kwargs,
        )

    async def call_transcription_async(
        self,
        audio_input: Union[str, AudioSegment],
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        **kwargs,
    ) -> Dict:
        """
        Call transcription API and return full response data using the orchestrator.

        Args:
            audio_input: Either file path (str) or AudioSegment object
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            **kwargs: Additional provider-specific parameters

        Returns:
            Dict containing full transcription response data

        Raises:
            AudioFileError: If there are issues with the audio input
            AudioTranscriptionError: If all providers fail
        """
        return await self._call_with_fallback(
            "call_transcription_async",
            audio_input=audio_input,
            language=language,
            use_cache=use_cache,
            response_usage=response_usage,
            max_file_size_mb=max_file_size_mb,
            **kwargs,
        )

    async def generate_transcription(
        self,
        audio_segment: AudioSegment,
        language: str = "ro",
        response_usage: Optional[ResponseUsage] = None,
    ) -> tuple[str, dict[str, str]]:
        """
        Generate transcription using the primary provider's specific implementation.

        Args:
            audio_segment: AudioSegment to transcribe
            language: Language code for transcription
            response_usage: Optional ResponseUsage object to track costs

        Returns:
            Tuple of (transcribed_text, chunks_status_dict)
        """
        provider_instance = self._providers[self.primary_provider]

        # Call the provider-specific _generate_transcription method
        return await provider_instance._generate_transcription(
            audio_segment=audio_segment,
            language=language,
            response_usage=response_usage,
        )

    async def _call_with_fallback(self, method_name: str, **kwargs):
        """
        Call a provider method with fallback logic.

        Args:
            method_name: Name of the method to call on the provider
            **kwargs: Arguments to pass to the provider method

        Returns:
            Result from the provider method

        Raises:
            AudioTranscriptionError: If all providers fail
        """
        providers_to_try = [self.primary_provider] + self.fallback_providers

        last_error = None

        for current_provider in providers_to_try:
            if current_provider not in self._providers:
                logger.warning(f"Provider {current_provider} not available, skipping")
                continue

            provider_instance = self._providers[current_provider]

            # Try the provider with retries
            for attempt in range(self.max_retries):
                try:
                    logger.info(
                        f"Attempting transcription with {current_provider} (attempt {attempt + 1}/{self.max_retries})"
                    )

                    # Get the method from the provider instance
                    method = getattr(provider_instance, method_name)

                    # Set default max_file_size_mb if not provided
                    if kwargs.get("max_file_size_mb") is None:
                        kwargs["max_file_size_mb"] = (
                            provider_instance.get_max_file_size_mb()
                        )

                    # Call the provider method
                    result = await method(**kwargs)

                    logger.info(f"Successfully transcribed with {current_provider}")
                    return result

                except (AudioFileError, AudioTranscriptionError) as e:
                    last_error = e
                    logger.warning(
                        f"Provider {current_provider} failed (attempt {attempt + 1}): {e}"
                    )

                    # If it's a file error, don't retry with the same provider
                    if isinstance(e, AudioFileError):
                        break

                    # Wait before retrying (exponential backoff)
                    if attempt < self.max_retries - 1:
                        wait_time = self.retry_delay * (2**attempt)
                        logger.info(f"Waiting {wait_time}s before retry...")
                        import asyncio

                        await asyncio.sleep(wait_time)

                except Exception as e:
                    last_error = AudioTranscriptionError(
                        f"Unexpected error with {current_provider}: {e}"
                    )
                    logger.error(f"Unexpected error with {current_provider}: {e}")
                    break

        # If we get here, all providers failed
        error_msg = f"All transcript providers failed. Last error: {last_error}"
        logger.error(error_msg)
        raise AudioTranscriptionError(error_msg)


async def test():
    """Test the transcript orchestrator."""
    from pydub import AudioSegment
    from acva_ai.utils.usage import ResponseUsage
    from acva_ai._params import DEFAULT_TRANSCRIPT_PROVIDER

    response_usage = ResponseUsage()

    # Create orchestrator with default provider from environment
    orchestrator = TranscriptOrchestrator(
        primary_provider=TranscriptProvider(DEFAULT_TRANSCRIPT_PROVIDER),
    )

    # Test with a sample audio file
    sample_file = "/home/<USER>/Desktop/Sapio/acva-ai/acva_audios/concat_audio/e49e0f84-12e9-4232-97f5-64fa162ea070.wav"
    print(f"Testing transcription with audio file: {sample_file}")

    try:
        result_file = await orchestrator.transcribe_audio_file(
            audio_file_path=sample_file,
            use_cache=False,
            response_usage=response_usage,
        )
        print("Transcription result (audio file):")
        print(result_file)

        audio_segment = AudioSegment.from_file(sample_file)
        print(f"Testing with audio segment: {len(audio_segment)}ms duration")

        result_segment = await orchestrator.transcribe_audio_segment(
            audio_segment=audio_segment,
            use_cache=False,
            response_usage=response_usage,
        )
        print("Transcription result (audio segment):")
        print(result_segment)

    except (AudioFileError, AudioTranscriptionError) as e:
        print(f"Error during transcription: {e}")

    # Test with explicit provider selection
    orchestrator = TranscriptOrchestrator(
        primary_provider=TranscriptProvider("azure"),
    )
    try:
        result_file = await orchestrator.transcribe_audio_file(
            audio_file_path=sample_file,
            use_cache=False,
            response_usage=response_usage,
        )
        print("Transcription result (audio file):")
        print(result_file)

        audio_segment = AudioSegment.from_file(sample_file)
        print(f"Testing with audio segment: {len(audio_segment)}ms duration")

        result_segment = await orchestrator.transcribe_audio_segment(
            audio_segment=audio_segment,
            use_cache=False,
            response_usage=response_usage,
        )
        print("Transcription result (audio segment):")
        print(result_segment)

    except (AudioFileError, AudioTranscriptionError) as e:
        print(f"Error during transcription: {e}")
    print(f"Usage: {response_usage}")


if __name__ == "__main__":
    import asyncio

    asyncio.run(test())
