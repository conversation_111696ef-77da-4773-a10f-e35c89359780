"""
OpenAI transcript provider implementation using the standard OpenAI library.
"""

import asyncio
import hashlib
import json
import os
import tempfile
from typing import Dict, Optional, Union

from pydub import AudioSegment

from acva_ai._params import (
    OPENAI_API,
    OPENAI_MAX_AUDIO_SIZE,
    OPENAI_TRANSCRIPTION_MODEL_ID,
)


from acva_ai.llm.llm_cache import LLM_AUDIO_CACHE_DIR
from acva_ai.utils.usage import ResponseUsage
from acva_ai.audio_processing.audio_splitting import (
    merge_audio_chunks_by_duration,
    split_audio_segment_by_silence,
)
from acva_ai.llm.providers.transcript_helpers import (
    AudioTranscriptionError,
    AudioFileError,
    get_audio_duration_from_file,
    get_audio_duration_from_segment,
    validate_file_size,
    validate_audio_file,
    calculate_usage_cost_from_file,
    calculate_usage_cost_from_segment,
    standardize_response,
    load_cached_response,
    save_to_cache,
)

# Import OpenAI library
try:
    from openai import OpenAI
except ImportError:
    raise ImportError(
        "OpenAI library is required for OpenAI transcript provider. Install with: pip install openai"
    )


class OpenAITranscriptProvider:
    """
    OpenAI transcript provider implementation using the standard OpenAI library.

    This provider uses OpenAI's Whisper API for audio transcription.
    It supports various audio formats and provides multiple response formats.
    """

    def __init__(self):
        """Initialize the OpenAI transcript provider."""
        self.provider_name = "openai"

        # Initialize OpenAI client
        if not OPENAI_API:
            raise AudioTranscriptionError("OPENAI_API environment variable is not set")

        self.client = OpenAI(
            api_key=OPENAI_API,
        )
        self.model_id = OPENAI_TRANSCRIPTION_MODEL_ID

    def get_max_file_size_mb(self) -> float:
        """Get the maximum file size supported by OpenAI."""
        return OPENAI_MAX_AUDIO_SIZE

    def get_provider_name(self) -> str:
        """Get the provider name."""
        return self.provider_name

    async def _calculate_usage_cost_from_file(
        self,
        audio_file_path: str,
        response_data: Dict,
        response_usage: ResponseUsage,
    ) -> None:
        """Calculate and track usage costs for file-based transcription."""
        await calculate_usage_cost_from_file(
            self.model_id, audio_file_path, response_data, response_usage
        )

    async def _calculate_usage_cost_from_segment(
        self,
        audio_segment: AudioSegment,
        response_data: Dict,
        response_usage: ResponseUsage,
    ) -> None:
        """Calculate and track usage costs for AudioSegment-based transcription."""
        await calculate_usage_cost_from_segment(
            self.model_id, audio_segment, response_data, response_usage
        )

    async def transcribe_audio_file(
        self,
        audio_file_path: str,
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        response_format: str = "json",
        temperature: float = 0,
        **kwargs,
    ) -> str:
        """
        Transcribe audio from a file path using OpenAI.

        Args:
            audio_file_path: Path to the audio file
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            response_format: Format of the response ('json', 'text', 'srt', 'verbose_json', 'vtt')
            temperature: Sampling temperature (0 to 1)
            **kwargs: Additional OpenAI-specific parameters

        Returns:
            Transcribed text as string

        Raises:
            AudioFileError: If there are issues with the audio file
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            result = await self.call_transcription_async(
                audio_input=audio_file_path,
                language=language,
                use_cache=use_cache,
                response_usage=response_usage,
                max_file_size_mb=max_file_size_mb,
                response_format=response_format,
                temperature=temperature,
                **kwargs,
            )
            return result.get("text", "")
        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"OpenAI file transcription error: {e}")

    async def transcribe_audio_segment(
        self,
        audio_segment: AudioSegment,
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        response_format: str = "json",
        temperature: float = 0,
        **kwargs,
    ) -> str:
        """
        Transcribe audio from an AudioSegment using OpenAI.

        Args:
            audio_segment: AudioSegment object to transcribe
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            response_format: Format of the response ('json', 'text', 'srt', 'verbose_json', 'vtt')
            temperature: Sampling temperature (0 to 1)
            **kwargs: Additional OpenAI-specific parameters

        Returns:
            Transcribed text as string

        Raises:
            AudioFileError: If there are issues with the AudioSegment
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            result = await self.call_transcription_async(
                audio_input=audio_segment,
                language=language,
                use_cache=use_cache,
                response_usage=response_usage,
                max_file_size_mb=max_file_size_mb,
                response_format=response_format,
                temperature=temperature,
                **kwargs,
            )
            return result.get("text", "")
        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"OpenAI segment transcription error: {e}")

    async def call_transcription_async(
        self,
        audio_input: Union[str, AudioSegment],
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        response_format: str = "json",
        temperature: float = 0,
        **kwargs,
    ) -> Dict:
        """
        Call OpenAI transcription API and return full response data.

        Args:
            audio_input: Either file path (str) or AudioSegment object
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            response_format: Format of the response ('json', 'text', 'srt', 'verbose_json', 'vtt')
            temperature: Sampling temperature (0 to 1)
            **kwargs: Additional OpenAI-specific parameters

        Returns:
            Dict containing full transcription response data

        Raises:
            AudioFileError: If there are issues with the audio input
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            # Set defaults
            max_file_size_mb = max_file_size_mb or self.get_max_file_size_mb()

            # Handle different input types
            if isinstance(audio_input, str):
                # File path input
                audio_file_path = audio_input
                file_content = validate_audio_file(audio_file_path)
                validate_file_size(audio_file_path, max_file_size_mb)

                # Generate cache key
                content_hash = hashlib.md5(file_content).hexdigest()

            else:
                # AudioSegment input
                audio_segment = audio_input

                # Create temporary file for AudioSegment
                with tempfile.NamedTemporaryFile(
                    suffix=".wav", delete=False
                ) as temp_file:
                    temp_path = temp_file.name

                try:
                    # Export AudioSegment to temporary file
                    audio_segment.export(temp_path, format="wav")
                    audio_file_path = temp_path
                    file_content = validate_audio_file(audio_file_path)
                    validate_file_size(audio_file_path, max_file_size_mb)

                    # Generate cache key
                    content_hash = hashlib.md5(file_content).hexdigest()

                except Exception as e:
                    # Clean up temp file on error
                    try:
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                    except:
                        pass
                    raise e

            # Setup cache
            cache_key = f"{content_hash}_{self.model_id}_{language or 'auto'}"
            cache_filename = f"{cache_key}.json"
            cache_filepath = os.path.join(LLM_AUDIO_CACHE_DIR, cache_filename)

            # Try to load from cache
            if use_cache:
                cached_response = load_cached_response(cache_filepath)
                if cached_response is not None:
                    return cached_response

            # Make transcription request using OpenAI library
            try:
                with open(audio_file_path, "rb") as audio_file:
                    # Prepare transcription parameters
                    transcription_params = {
                        "file": audio_file,
                        "model": self.model_id,
                        "response_format": response_format,
                        "temperature": temperature,
                    }

                    if language is not None:
                        transcription_params["language"] = language

                    # Call OpenAI transcription
                    response = self.client.audio.transcriptions.create(
                        **transcription_params
                    )

                    # Convert response to dict format
                    if response_format == "json" or response_format == "verbose_json":
                        if hasattr(response, "text"):
                            response_data = {
                                "text": response.text,
                                "language": getattr(response, "language", None),
                            }
                        else:
                            # Handle string response
                            response_data = {"text": str(response)}
                    else:
                        # For other formats (text, srt, vtt), response is a string
                        response_data = {"text": str(response)}

            except Exception as e:
                raise AudioTranscriptionError(f"OpenAI API error: {e}")

            finally:
                # Clean up temporary file if it was created for AudioSegment
                if isinstance(audio_input, AudioSegment):
                    try:
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                    except Exception as cleanup_error:
                        print(
                            f"Warning: Could not cleanup temp file {temp_path}: {cleanup_error}"
                        )

            # Calculate usage if requested
            if response_usage is not None:
                if isinstance(audio_input, str):
                    await calculate_usage_cost_from_file(
                        audio_file_path, response_data, response_usage
                    )
                else:
                    await calculate_usage_cost_from_segment(
                        audio_input, response_data, response_usage
                    )

            # Cache the response
            if use_cache:
                save_to_cache(cache_filepath, response_data)

            # Standardize the response format
            return standardize_response(response_data, self.provider_name)

        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"OpenAI API call error: {e}")

    async def _generate_transcription(
        self,
        audio_segment: AudioSegment,
        batch_size: int = 25,
        min_duration_seconds: float = 1 * 60,
        max_duration_seconds: float = 2 * 60,
        language: str = "ro",
        response_usage: Optional[ResponseUsage] = None,
    ) -> str:
        """
        OpenAI-specific transcription generation with play-by-silence approach.

        Args:
            audio_segment: AudioSegment to transcribe
            batch_size: Number of chunks to process in parallel
            min_duration_seconds: Minimum duration for merged chunks
            max_duration_seconds: Maximum duration for chunks
            language: Language code for transcription
            response_usage: Optional ResponseUsage object to track costs

        Returns:
            Transcribed text as string
        """
        # Split audio by silence first for better quality
        audio_segment_chunks = split_audio_segment_by_silence(
            audio_segment=audio_segment, max_duration_seconds=max_duration_seconds
        )
        print(
            f"OpenAI: Initial silence split created {len(audio_segment_chunks)} chunks"
        )

        # Merge chunks to fit within min/max duration constraints
        merged_chunks = merge_audio_chunks_by_duration(
            audio_segment_chunks,
            min_duration_seconds=min_duration_seconds,
            max_duration_seconds=max_duration_seconds,
        )
        print(f"OpenAI: After merging, {len(merged_chunks)} chunks remain")
        final_chunks = merged_chunks
        print(f"OpenAI: Processing {len(final_chunks)} final chunks")

        # Process chunks in batches
        audio_segment_chunks_batches = [
            final_chunks[i : i + batch_size]
            for i in range(0, len(final_chunks), batch_size)
        ]

        result_transcriptions = []

        for batch in audio_segment_chunks_batches:
            tasks = [
                self.transcribe_audio_segment(
                    audio_segment=audio_segment_chunk,
                    language=language,
                    response_usage=response_usage,
                )
                for audio_segment_chunk in batch
            ]
            results = await asyncio.gather(*tasks)
            result_transcriptions.extend(results)
        result_transcript = " ".join(result_transcriptions)

        return result_transcript
