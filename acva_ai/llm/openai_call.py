import asyncio
import hashlib
import json
import logging
import os
import re
from typing import Dict, List, Optional, Union

import aiohttp

from acva_ai._params import CACHE_DIR, OPENAI_API
from acva_ai.utils.usage import LLMUsage, ResponseUsage

logger = logging.getLogger(__name__)

# Set up cache directory
OPENAI_CACHE_DIR = os.path.join(CACHE_DIR, "openai")
os.makedirs(OPENAI_CACHE_DIR, exist_ok=True)


def _generate_openai_cache_filename(
    prompt: Union[str, List[Dict]], model_id: str, max_tokens: int
) -> str:
    """
    Generate a stable filename for the given prompt parameters.

    Args:
        prompt: The prompt or list of messages
        model_id: The OpenAI model ID
        max_tokens: Maximum tokens to generate

    Returns:
        Path to the cache file
    """
    # Convert to string for hashing
    prompt_str = json.dumps(prompt) if isinstance(prompt, list) else prompt

    # Create a unique hash based on the prompt, model, and max_tokens
    hash_input = f"{prompt_str}_{model_id}_{max_tokens}"
    prompt_hash = hashlib.md5(hash_input.encode("utf-8")).hexdigest()
    cache_filename = f"{model_id}_{prompt_hash}.json"
    return os.path.join(OPENAI_CACHE_DIR, cache_filename)


async def call_openai_async(
    prompt: Union[str, List[Dict]],
    model_id: str = "gpt-4o",
    max_tokens: int = 1000,
    use_cache: bool = False,
    response_usage: Optional[ResponseUsage] = None,
    current_retry: int = 0,
    max_retries: int = 5,
    retry_delay: float = 10,
    temperature: float = 0,
) -> str:
    """
    Asynchronous call to OpenAI API with caching and retry logic.

    Args:
        prompt: The prompt (string) or messages (list of dicts with role and content)
        model_id: The OpenAI model ID (default: "gpt-4o")
        max_tokens: Maximum tokens to generate
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs
        current_retry: Current retry attempt (used internally)
        max_retries: Maximum number of retries for rate limit errors
        retry_delay: Initial delay before retrying (will increase exponentially)
        temperature: Temperature parameter for the model

    Returns:
        The model's response as a string
    """
    task_id = None
    # Try to extract task ID from the current context if it's in the prompt
    if isinstance(prompt, str):
        task_id_match = re.search(r"\[Task ([a-f0-9-]+)\]", prompt)
        if task_id_match:
            task_id = task_id_match.group(1)

    log_prefix = f"[Task {task_id}] " if task_id else ""

    # Check cache first
    cache_filepath = _generate_openai_cache_filename(prompt, model_id, max_tokens)
    if os.path.isfile(cache_filepath) and use_cache:
        try:
            with open(cache_filepath, "r", encoding="utf-8") as f:
                cached_data = json.load(f)
                cached_response = cached_data.get("response", "")
            logger.info(f"{log_prefix}Loaded OpenAI response from cache.")
            return cached_response
        except Exception as e:
            logger.warning(f"{log_prefix}Error reading cache file: {e}")

    url = "https://api.openai.com/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {OPENAI_API}",
        "Content-Type": "application/json",
    }

    # Prepare the payload based on prompt type
    if isinstance(prompt, str):
        messages = [{"role": "system", "content": prompt}]
    else:
        messages = prompt

    payload = {
        "model": model_id,
        "messages": messages,
        "max_tokens": max_tokens,
        "temperature": temperature,
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response_obj:
                response_data = await response_obj.json()

                # Handle API errors
                if response_obj.status != 200 or "error" in response_data:
                    error = response_data.get("error", {})
                    error_type = error.get("type", "")
                    error_message = error.get("message", "Unknown error")

                    logger.warning(f"{log_prefix}OpenAI API error: {error_message}")

                    # Handle rate limiting errors
                    if (
                        error_type == "rate_limit_exceeded"
                        or response_obj.status == 429
                    ):
                        if current_retry < max_retries:
                            wait_time = retry_delay * (2**current_retry)
                            logger.warning(
                                f"{log_prefix}Rate limit reached. Waiting {wait_time} seconds..."
                            )
                            await asyncio.sleep(wait_time)
                            return await call_openai_async(
                                prompt=prompt,
                                model_id=model_id,
                                max_tokens=max_tokens,
                                use_cache=use_cache,
                                response_usage=response_usage,
                                current_retry=current_retry + 1,
                                max_retries=max_retries,
                                retry_delay=retry_delay,
                                temperature=temperature,
                            )

                    # Handle server errors
                    if response_obj.status >= 500 or error_type == "server_error":
                        if current_retry < max_retries:
                            wait_time = retry_delay * (2**current_retry)
                            logger.warning(
                                f"{log_prefix}Server error. Waiting {wait_time} seconds..."
                            )
                            await asyncio.sleep(wait_time)
                            return await call_openai_async(
                                prompt=prompt,
                                model_id=model_id,
                                max_tokens=max_tokens,
                                use_cache=use_cache,
                                response_usage=response_usage,
                                current_retry=current_retry + 1,
                                max_retries=max_retries,
                                retry_delay=retry_delay,
                                temperature=temperature,
                            )

                    # If we've exhausted retries or it's another type of error
                    if current_retry >= max_retries:
                        logger.error(
                            f"{log_prefix}Error maintained after {max_retries} retries: {error_message}"
                        )
                        raise Exception(f"OpenAI API error: {error_message}")

                    # For other errors, retry once
                    wait_time = retry_delay
                    logger.warning(
                        f"{log_prefix}Unexpected error. Retrying in {wait_time} seconds..."
                    )
                    await asyncio.sleep(wait_time)
                    return await call_openai_async(
                        prompt=prompt,
                        model_id=model_id,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        current_retry=current_retry + 1,
                        max_retries=max_retries,
                        retry_delay=retry_delay,
                        temperature=temperature,
                    )

                # Extract the response text
                response_msg = response_data["choices"][0]["message"]["content"]

                # Track usage if provided
                if response_usage is not None and "usage" in response_data:
                    input_tokens = response_data["usage"]["prompt_tokens"]
                    output_tokens = response_data["usage"]["completion_tokens"]

                    # Calculate cost based on model pricing
                    # Note: You would need to add pricing info to your _params.py file
                    # similar to OPENAI_PRICING in azure_call.py
                    from acva_ai._params import OPENAI_PRICING

                    cost = None
                    model_pricing_dict = OPENAI_PRICING.get(model_id)
                    if model_pricing_dict is not None:
                        input_cost = model_pricing_dict["input"] * input_tokens * 10e-6
                        output_cost = (
                            model_pricing_dict["output"] * output_tokens * 10e-6
                        )
                        cost = input_cost + output_cost

                    llm_usage = LLMUsage(
                        model_id=model_id,
                        cost=cost,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                    )

                    response_usage.add_llm_usage(llm_usage)

                # Cache the response
                try:
                    cache_data = {
                        "response": response_msg,
                        "usage": response_data.get("usage", {}),
                    }
                    with open(cache_filepath, "w", encoding="utf-8") as f:
                        json.dump(cache_data, f, ensure_ascii=False, indent=2)
                except Exception as e:
                    logger.warning(f"{log_prefix}Error writing to cache file: {e}")

                return response_msg

    except aiohttp.ClientError as e:
        logger.error(f"{log_prefix}Connection error: {str(e)}")

        if current_retry < max_retries:
            wait_time = retry_delay * (2**current_retry)
            logger.warning(
                f"{log_prefix}Connection error. Retrying in {wait_time} seconds..."
            )
            await asyncio.sleep(wait_time)
            return await call_openai_async(
                prompt=prompt,
                model_id=model_id,
                max_tokens=max_tokens,
                use_cache=use_cache,
                response_usage=response_usage,
                current_retry=current_retry + 1,
                max_retries=max_retries,
                retry_delay=retry_delay,
                temperature=temperature,
            )
        else:
            raise


def test():
    """Test the OpenAI API call function."""
    response_usage = ResponseUsage()

    # Test with a simple prompt
    result = asyncio.run(
        call_openai_async(
            prompt="What is the capital of France?",
            model_id="gpt-4o",
            max_tokens=1000,
            response_usage=response_usage,
            use_cache=True,  # Enable caching
        )
    )
    print(result)
    print(response_usage)

    # Test with a message list
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of Italy?"},
    ]
    result_messages = asyncio.run(
        call_openai_async(
            prompt=messages,
            model_id="gpt-4o",
            max_tokens=1000,
            response_usage=response_usage,
            use_cache=False,  # Enable caching
        )
    )
    print(result_messages)
    print(response_usage)


if __name__ == "__main__":
    test()
